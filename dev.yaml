services:
  db:
    image: postgres:latest
    container_name: finance-erp-db-dev
    environment:
      POSTGRES_DB: finance-erp
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    ports:
      - 5432:5432
    volumes:
      - finance-erp-postgres-data-dev:/var/lib/postgresql/data

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - 5672:5672
      - 15672:15672
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  finance-erp-postgres-data-dev:
  rabbitmq_data: