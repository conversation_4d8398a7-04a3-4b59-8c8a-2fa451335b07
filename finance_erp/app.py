import logging
import os
import time
from inspect import getmembers, isfunction

import flask
import sentry_sdk
from flask import Blueprint, Flask, request
from flask_log_request_id import RequestID
from flask_login import LoginManager
from sentry_sdk.integrations.flask import FlaskIntegration
from sentry_sdk.integrations.logging import ignore_logger
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import request_context
from treebo_commons.request_tracing.flask import after_request, before_request
from treebo_commons.request_tracing.flask.after_request import clear_request_context
from werkzeug.routing import BaseConverter

from finance_erp.async_job.cli.job_scheduler import async_job_scheduler
from finance_erp.audit_extension import audit_ext
from finance_erp.cli.consumers.company_profile_consumer import company_profile_consumer
from finance_erp.cli.consumers.crs_consumer import start_crs_consumer
from finance_erp.cli.schedule_invoice_dispatch import schedule_invoice_dispatch
from finance_erp.cli.schedule_stay_summary_dispatch import (
    schedule_stay_summary_dispatch,
)
from finance_erp.commands.create_admin_user import create_admin_user
from finance_erp.commands.update_transaction_master_entries import (
    update_transaction_master_entries,
)
from finance_erp.common.globals import finance_erp_context
from finance_erp.config.log_conf import configure_logging
from finance_erp.domain.common.audit.audit import attach_auditing
from finance_erp.middlewares.common_middlewares import (
    before_request as sentry_before_request,
)
from finance_erp.middlewares.common_middlewares import (
    exception_handler,
    filter_sentry_events,
)
from object_registry import finalize_app_initialization, inject

logger = logging.getLogger(__name__)


def register_jinja_filters(app):
    from finance_erp import jinja_filters

    for name, func in getmembers(jinja_filters):
        if isfunction(func):
            app.jinja_env.filters[name] = func  # pylint: disable=no-member


def register_blueprints(app):
    # pylint suppression is because of urls import * which should be removed
    # pylint: disable=redefined-outer-name
    from finance_erp import blueprints

    for _, func in getmembers(blueprints):
        if isinstance(func, Blueprint):
            app.register_blueprint(func, url_prefix=f"/erp{func.url_prefix}")


def register_extensions(app):
    """
    Registering extensions
    :param app:
    :return:
    """
    audit_ext.init_app(app)


def setup_app_config(app):
    finance_erp_settings = os.getenv(
        "FINANCE_ERP_SETTINGS", "finance_erp.config.local.LocalConfig"
    )
    print(f"App starting up using settings: {finance_erp_settings}")
    app.config.from_object(finance_erp_settings)


def register_error_handlers(app):
    app.register_error_handler(Exception, exception_handler)


def custom_after_request(res, req):
    response = after_request(res, req)
    request_context.clear()
    finance_erp_context.clear()
    return response


def setup_request_id(app):
    RequestID(app)

    def set_request_time():
        request_context.start_time = time.time()

    app.before_request(set_request_time)
    app.before_request(lambda: custom_before_request(flask.request))
    app.after_request(lambda resp: custom_after_request(resp, request))


def custom_before_request(req):
    if isinstance(req, dict):
        request_headers = req
    else:
        request_headers = dict(req.headers)
    before_request(req)
    sentry_before_request(request_headers)
    if "ADMIN_TENANT_ID" in os.environ:
        logger.info(
            "ADMIN_TENANT_ID: %s, found in environment. Overriding that in context.",
            os.environ.get("ADMIN_TENANT_ID"),
        )
        request_context.tenant_id = os.environ.get(
            "ADMIN_TENANT_ID", TenantClient.get_default_tenant()
        )


def register_commands(app):
    print("Registering commands")
    from finance_erp.async_job.cli.job_executor import async_job_executor

    app.cli.add_command(async_job_executor)
    app.cli.add_command(async_job_scheduler)
    app.cli.add_command(start_crs_consumer)
    app.cli.add_command(company_profile_consumer)
    app.cli.add_command(schedule_stay_summary_dispatch)
    app.cli.add_command(schedule_invoice_dispatch)
    app.cli.add_command(update_transaction_master_entries)
    app.cli.add_command(create_admin_user)


class RegexConverter(BaseConverter):
    def __init__(self, url_map, *items):
        super(RegexConverter, self).__init__(url_map)
        self.regex = items[0]


def setup_health_check(app):
    @app.route("/health", methods=["GET"])
    def health_check():
        """
        test api to check the health of the service
        :return:
        """
        return "ok"


def setup_user_loader(login_manager):
    from finance_erp.domain.auth.repository.user_repository import UserRepository

    @login_manager.user_loader
    @inject(user_repository=UserRepository)
    def load_user(email, user_repository: UserRepository):
        return user_repository.find(email=email)


def init_sentry():
    environment = os.environ.get("APP_ENV", "local")
    sentry_sdk.init(
        integrations=[FlaskIntegration(), SqlalchemyIntegration()],
        release=os.environ.get("BUILD_NUMBER"),
        environment=environment,
        traces_sample_rate=float(os.environ.get("SENTRY_SAMPLE_RATE", "0.01")),
        before_send=filter_sentry_events,
    )
    sentry_sdk.set_tag("AWS_REGION", os.environ.get("AWS_REGION"))
    sentry_sdk.set_tag("CLUSTER_IDENTIFIER", os.environ.get("CLUSTER_IDENTIFIER"))
    ignore_logger("finance_erp.middlewares.common_middlewares")


def create_app():
    init_sentry()
    app = Flask(__name__, static_url_path="/erp/static")
    setup_app_config(app)
    configure_logging(app)
    setup_request_id(app)
    register_error_handlers(app)
    app.url_map.converters["regex"] = RegexConverter
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = "login"
    login_manager.session_protection = "strong"

    register_jinja_filters(app)
    register_blueprints(app)
    app.url_map.strict_slashes = False
    register_commands(app)
    finalize_app_initialization()

    # pylint: disable=ungrouped-imports,wrong-import-position
    if os.environ.get("APP_ENV") != "testing":
        from finance_erp.admin import setup_admin

        tenant_id = os.environ.get("ADMIN_TENANT_ID", TenantClient.get_default_tenant())
        setup_admin(app, tenant_id)

    @app.teardown_request
    def shutdown_session(exception=None):
        if os.environ.get("APP_ENV") != "testing":
            db_engine.remove_session()

    @app.teardown_appcontext
    def clear_thread_local(exception=None):

        if os.environ.get("APP_ENV") == "testing":
            db_engine.remove_session()

        clear_request_context()

    setup_health_check(app)

    setup_user_loader(login_manager)
    attach_auditing()

    return app
