import json
from contextlib import contextmanager

from finance_erp.application.hotel_settings.tenant_settings import TenantSettings


def send_request(
    client,
    method,
    url,
    payload=None,
    expected_status_code=200,
    return_full_response=False,
    hotel_id=None,
):
    url = f"/erp{url}"
    data_key = "query_string" if method == "get" else "json"
    kwargs = {data_key: payload, "content_type": "application/json"}
    if hotel_id:
        kwargs.update({"headers": {"X-Hotel-Id": hotel_id}})
    with mocked_context():
        response = getattr(client, method)(
            url,
            **kwargs,
        )

    assert response.status_code == expected_status_code

    if response.data:
        if return_full_response:
            return response.json
        return json.loads(response.data)["data"][0] if method == "get" else None


# Hotel-related functions
def create_hotel(client, payload, expected_status_code=200):
    url = "/api/v1/hotel-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_hotel(client, payload, expected_status_code=200):
    url = "/api/v1/hotel-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_hotel(client, payload, expected_status_code=200):
    url = "/api/v1/hotel-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_hotel(client, payload, expected_status_code=200):
    url = "/api/v1/hotel-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


# Corporate-related functions
def create_corporate(client, payload, expected_status_code=200):
    url = "/api/v1/corporate-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_corporate(client, payload, expected_status_code=200):
    url = "/api/v1/corporate-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_corporate(client, payload, expected_status_code=200):
    url = "/api/v1/corporate-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_corporate(client, payload, expected_status_code=200):
    url = "/api/v1/corporate-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def create_ota(client, payload, expected_status_code=200):
    url = "/api/v1/ota-commission-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_ota(client, payload, expected_status_code=200):
    url = "/api/v1/ota-commission-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_ota(client, payload, expected_status_code=200):
    url = "/api/v1/ota-commission-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_ota(client, payload, expected_status_code=200):
    url = "/api/v1/ota-commission-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def create_payment(client, payload, expected_status_code=200):
    url = "/api/v1/payment-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def aggregate_payment(client, payload, expected_status_code=200):
    url = "/api/v1/pg_payments-summary/generate"
    send_request(client, "post", url, payload, expected_status_code)


def get_payment(client, payload, expected_status_code=200):
    url = "/api/v1/payment-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_payment(client, payload, expected_status_code=200):
    url = "/api/v1/payment-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_payment(client, payload, expected_status_code=200):
    url = "/api/v1/payment-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def create_purchase(client, payload, expected_status_code=200):
    url = "/api/v1/purchase-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_purchase(client, payload, expected_status_code=200):
    url = "/api/v1/purchase-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_purchase(client, payload, expected_status_code=200):
    url = "/api/v1/purchase-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_purchase(client, payload, expected_status_code=200):
    url = "/api/v1/purchase-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def create_sales(client, payload, expected_status_code=200):
    url = "/api/v1/sales-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_sales(client, payload, expected_status_code=200):
    url = "/api/v1/sales-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_sales(client, payload, expected_status_code=200):
    url = "/api/v1/sales-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_sales(client, payload, expected_status_code=200):
    url = "/api/v1/sales-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def generate_sales_summary(client, payload, expected_status_code=200):
    url = "/api/v1/sales-summary/generate"
    send_request(client, "post", url, payload, expected_status_code)


def create_expense_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/expense-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_expense_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/expense-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_expense_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/expense-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_expense_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/expense-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def create_hotel_adjustment(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/hotel-adjustment-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_hotel_adjustment(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/hotel-adjustment-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_hotel_adjustment(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/hotel-adjustment-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_hotel_adjustment(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/hotel-adjustment-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def create_loan_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/loan-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_loan_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/loan-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_loan_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/loan-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_loan_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/loan-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def create_tax_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/tax-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_tax_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/tax-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_tax_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/tax-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_tax_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/tax-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def create_treebo_fee_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/treebo-fee-data/ingest"
    send_request(client, "post", url, payload, expected_status_code)


def get_treebo_fee_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/treebo-fee-data"
    return send_request(client, "get", url, payload, expected_status_code)


def update_treebo_fee_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/treebo-fee-data/bulk-update"
    send_request(client, "patch", url, payload, expected_status_code)


def bulk_push_treebo_fee_settlement(client, payload, expected_status_code=200):
    url = "/api/v1/settlement/treebo-fee-data/bulk-push"
    send_request(client, "post", url, payload, expected_status_code)


def create_pos_revenue_item(client, payload, expected_status_code=200):
    url = "/api/v1/pos/revenue-item/ingest"
    return send_request(
        client, "post", url, payload, expected_status_code, return_full_response=True
    )


def get_pos_revenue_items(client, payload, expected_status_code=200):
    url = "/api/v1/pos/revenue-item"
    return send_request(
        client, "get", url, payload, expected_status_code, return_full_response=True
    )


def generate_backoffice_ledgers(
    client, payload, expected_status_code=200, hotel_id=None
):
    url = "/api/v1/back-office/ledgers"
    return send_request(
        client,
        "post",
        url,
        payload,
        expected_status_code,
        return_full_response=True,
        hotel_id=hotel_id,
    )


@contextmanager
def mocked_context():
    act_get_business_central_blacklisted_hotels = (
        TenantSettings.get_business_central_blacklisted_hotels
    )

    def mocked_get_business_central_blacklisted_hotels(self):
        return []

    TenantSettings.get_business_central_blacklisted_hotels = (
        mocked_get_business_central_blacklisted_hotels
    )
    yield

    TenantSettings.get_business_central_blacklisted_hotels = (
        act_get_business_central_blacklisted_hotels
    )
