from datetime import datetime
from typing import Any, Literal, Optional

from pydantic import BaseModel, Field


class ERPEntityStatus:
    INGESTED = "ingested"
    PUSHED = "pushed"
    FAILED_TO_PUSH = "failed_to_push"
    EXCLUDED_FOR_DATA_PUSH = "excluded_for_data_push"

    @staticmethod
    def allowed_status_for_data_push():
        return [ERPEntityStatus.INGESTED, ERPEntityStatus.FAILED_TO_PUSH]

    @staticmethod
    def is_pushed(status):
        return status == ERPEntityStatus.PUSHED


ERPEntityStatusLiteral = Literal[
    ERPEntityStatus.FAILED_TO_PUSH,
    ERPEntityStatus.EXCLUDED_FOR_DATA_PUSH,
    ERPEntityStatus.INGESTED,
    ERPEntityStatus.PUSHED,
]


class ERPEntityMixin(BaseModel):
    status: ERPEntityStatusLiteral = ERPEntityStatus.INGESTED
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None

    def capture_data_push_failure(self, reason):
        self.status = ERPEntityStatus.FAILED_TO_PUSH
        self.last_push_attempt_at = datetime.now()
        self.erp_remarks = reason

    def capture_data_push_success(self):
        self.status = ERPEntityStatus.PUSHED
        self.last_push_attempt_at = datetime.now()
        self.erp_remarks = "Success"

    def mark_as_excluded_for_data_push(self, reason):
        self.status = ERPEntityStatus.EXCLUDED_FOR_DATA_PUSH
        self.erp_remarks = reason

    @property
    def pushed(self):
        return ERPEntityStatus.is_pushed(self.status)

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True
        validate_by_name = True


class ChangeTrackerMixin(BaseModel):
    old_state: Optional[Any] = Field(default=None, exclude=True)

    class Config:
        from_attributes = True
