from thsc.crs.entities.billing import Bill as T<PERSON><PERSON><PERSON>ill
from thsc.crs.entities.booking import Attachment as THSCAttachment
from thsc.crs.entities.booking import Booking as THSCBooking

from finance_erp.domain.crs.entity.booking import Booking
from finance_erp.domain.crs.value_objects.booking import Attachment


class BookingFactory:
    @staticmethod
    def build(
        thsc_booking: THSCBooking,
        thsc_bill: THSCBill,
        attachments: list[THSCAttachment],
    ):
        booker_legal_entity_id = None
        if (
            thsc_booking.travel_agent_details
            and thsc_booking.travel_agent_details.legal_details
        ):
            booker_legal_entity_id = (
                thsc_booking.travel_agent_details.legal_details.external_reference_id
            )
        elif (
            thsc_booking.company_details and thsc_booking.company_details.legal_details
        ):
            booker_legal_entity_id = (
                thsc_booking.company_details.legal_details.external_reference_id
            )
        data_dict = {
            "booking_id": thsc_booking.booking_id,
            "reference_number": thsc_booking.reference_number,
            "checkin_date": thsc_booking.checkin_date,
            "checkout_date": thsc_booking.checkout_date,
            "actual_checkin_date": thsc_booking.actual_checkin_date,
            "actual_checkout_date": thsc_booking.actual_checkout_date,
            "channel_code": thsc_booking.source.channel_code,
            "subchannel_code": thsc_booking.source.subchannel_code,
            "application_code": thsc_booking.source.application_code,
            "status": thsc_booking.status,
            "booker_legal_entity_id": booker_legal_entity_id,
            "total_booking_amount": thsc_bill.total_posttax_amount.amount,
            "attachments": [
                Attachment.from_thsc_attachment(attachment)
                for attachment in attachments
            ],
        }
        return Booking.model_validate(data_dict)
