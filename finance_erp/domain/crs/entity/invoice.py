from datetime import date, datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel
from ths_common.constants.billing_constants import InvoiceStatus
from ths_common.value_objects import InvoiceBillToInfo, InvoiceIssuedByInfo
from treebo_commons.money import Money

from finance_erp.common.constants import InvoiceTypes
from finance_erp.domain.crs.value_objects.booking import BookingMeta


class Invoice(BaseModel):
    invoice_id: str
    invoice_number: Optional[str] = None
    status: InvoiceStatus
    customer_external_id: Optional[str] = None
    booker_legal_entity_id: Optional[str] = None
    issued_to: Optional[InvoiceBillToInfo] = None
    issued_by: Optional[InvoiceIssuedByInfo] = None
    issued_to_type: str
    issued_by_type: str
    booking_reference_number: str
    booking_id: str
    is_b2b: bool
    pre_tax_amount: float
    tax_amount: float
    post_tax_amount: float
    due_date: Optional[date] = None
    invoice_date: date
    invoice_type: str
    is_spot_credit: bool
    invoice_url: Optional[str] = None
    hotel_id: str
    vendor_details: dict
    booking_meta: BookingMeta
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    frozen_at: Optional[datetime] = None
    dirty: bool = False

    def __init__(self, **data: Dict) -> None:
        super().__init__(**data)
        self._update_invoice_frozen_time_if_required()

    def _update_invoice_frozen_time_if_required(self):
        if self.frozen_at is None and self.status in (
            InvoiceStatus.LOCKED,
            InvoiceStatus.CANCELLED,
        ):
            self.frozen_at = datetime.now()
            self.dirty = True

    def update(self, other: "Invoice"):
        self._update_invoice_url(other.invoice_url)
        self._update_invoice_status(other.status)
        if self.frozen_at:
            return  # Do not update any other fields if the invoice is frozen
        self._update_pre_tax_amount(other.pre_tax_amount)
        self._update_tax_amount(other.tax_amount)
        self._update_post_tax_amount(other.post_tax_amount)
        self._update_due_date(other.due_date)
        self._update_invoice_date(other.invoice_date)
        self._update_is_spot_credit(other.is_spot_credit)
        self.update_booking_meta(other.booking_meta)
        self.update_vendor_details(other.vendor_details)
        self._update_customer_external_id(other.customer_external_id)
        self._update_booker_legal_entity_id(other.booker_legal_entity_id)
        self._update_issued_to(other.issued_to)
        self._update_issued_by(other.issued_by)
        self._update_issued_to_type(other.issued_to_type)
        self._update_issued_by_type(other.issued_by_type)
        self._update_booking_reference_number(other.booking_reference_number)
        self._update_booking_id(other.booking_id)
        self._update_is_b2b(other.is_b2b)
        self._update_invoice_type(other.invoice_type)
        self._update_hotel_id(other.hotel_id)

    def _update_invoice_status(self, status: Any):
        if status != self.status:
            self.status = status
            self._update_invoice_frozen_time_if_required()
            self.dirty = True

    def _update_invoice_url(self, invoice_url):
        if invoice_url is not None and invoice_url != self.invoice_url:
            self.invoice_url = invoice_url
            self.dirty = True

    def _update_pre_tax_amount(self, pre_tax_amount):
        if pre_tax_amount != self.pre_tax_amount:
            self.pre_tax_amount = pre_tax_amount
            self.dirty = True

    def _update_tax_amount(self, tax_amount):
        if tax_amount != self.tax_amount:
            self.tax_amount = tax_amount
            self.dirty = True

    def _update_post_tax_amount(self, post_tax_amount):
        if post_tax_amount != self.post_tax_amount:
            self.post_tax_amount = post_tax_amount
            self.dirty = True

    @property
    def due_amount(self):
        if InvoiceTypes.is_credit(self.invoice_type):
            return self.post_tax_amount
        return 0

    @property
    def paid_amount(self):
        if self.invoice_type == InvoiceTypes.NON_CREDIT:
            return self.post_tax_amount
        return 0

    def _update_due_date(self, due_date):
        if due_date != self.due_date:
            self.due_date = due_date
            self.dirty = True

    def _update_invoice_date(self, invoice_date):
        if invoice_date != self.invoice_date:
            self.invoice_date = invoice_date
            self.dirty = True

    def _update_is_spot_credit(self, is_spot_credit):
        if is_spot_credit != self.is_spot_credit:
            self.is_spot_credit = is_spot_credit
            self.dirty = True

    def update_booking_meta(self, booking_meta):
        if booking_meta != self.booking_meta:
            self.booking_meta = booking_meta
            self.dirty = True

    def _update_customer_external_id(self, customer_external_id):
        if customer_external_id != self.customer_external_id:
            self.customer_external_id = customer_external_id
            self.dirty = True

    def _update_booker_legal_entity_id(self, booker_legal_entity_id):
        if booker_legal_entity_id != self.booker_legal_entity_id:
            self.booker_legal_entity_id = booker_legal_entity_id
            self.dirty = True

    def _update_issued_to(self, issued_to):
        if issued_to != self.issued_to:
            self.issued_to = issued_to
            self.dirty = True

    def _update_issued_by(self, issued_by):
        if issued_by != self.issued_by:
            self.issued_by = issued_by
            self.dirty = True

    def _update_issued_to_type(self, issued_to_type):
        if issued_to_type != self.issued_to_type:
            self.issued_to_type = issued_to_type
            self.dirty = True

    def _update_issued_by_type(self, issued_by_type):
        if issued_by_type != self.issued_by_type:
            self.issued_by_type = issued_by_type
            self.dirty = True

    def _update_booking_reference_number(self, booking_reference_number):
        if booking_reference_number != self.booking_reference_number:
            self.booking_reference_number = booking_reference_number
            self.dirty = True

    def _update_booking_id(self, booking_id):
        if booking_id != self.booking_id:
            self.booking_id = booking_id
            self.dirty = True

    def _update_is_b2b(self, is_b2b):
        if is_b2b != self.is_b2b:
            self.is_b2b = is_b2b
            self.dirty = True

    def _update_invoice_type(self, invoice_type):
        if invoice_type != self.invoice_type:
            self.invoice_type = invoice_type
            self.dirty = True

    def _update_hotel_id(self, hotel_id):
        if hotel_id != self.hotel_id:
            self.hotel_id = hotel_id
            self.dirty = True

    def update_vendor_details(self, vendor_details):
        if self.vendor_details != vendor_details:
            self.vendor_details = vendor_details
            self.dirty = True

    @property
    def url(self):
        return self.invoice_url

    @property
    def doc_date(self):
        return self.invoice_date

    @property
    def number(self):
        return self.invoice_number

    @property
    def uuid(self):
        return self.invoice_id

    @property
    def category(self):
        return self.invoice_type

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
