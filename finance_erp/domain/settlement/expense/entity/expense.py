# -*- coding: utf-8 -*-
from datetime import date, datetime
from typing import Optional

from finance_erp.domain.base import ERPEntityMixin, ERPEntityStatus

UPDATABLE_FIELDS = ["verified"]


class ExpenseEntity(ERPEntityMixin):
    hotel_code: str
    posting_date: date
    remarks: str
    uu_id: str
    tds_per: float
    entry_type: str
    hsn_code: str
    doc_type: str
    verified: bool = True
    status: str = ERPEntityStatus.INGESTED
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None
    invoice_number: Optional[str] = None
    invoice_amount: float = 0.0
    invoice_date: Optional[date] = None
    cost_center_id: Optional[str] = None

    def to_json(self):
        return self.model_dump()

    def get_unique_identifier(self):
        return self.uu_id

    def update(self, data: dict):
        self.remarks = data.get("remarks")
        self.tds_per = data.get("tds_per")
        self.hotel_code = data.get("hotel_code")
        self.invoice_number = data.get("invoice_number")
        self.invoice_amount = data.get("invoice_amount")
        self.invoice_date = data.get("invoice_date")
        self.entry_type = data.get("entry_type")
        self.hsn_code = data.get("hsn_code")
        self.doc_type = data.get("doc_type")
        self.posting_date = data.get("posting_date")
        self.verified = True
        self.status = ERPEntityStatus.INGESTED

    class Config:
        from_attributes = True
        validate_by_name = True
