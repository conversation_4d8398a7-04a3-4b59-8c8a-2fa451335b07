# -*- coding: utf-8 -*-
from datetime import date, datetime
from typing import Optional

from finance_erp.domain.base import ERPEntityMixin, ERPEntityStatus

UPDATABLE_FIELDS = ["verified"]


class HotelAdjustmentEntity(ERPEntityMixin):
    hotel_code: str
    posting_date: date
    remarks: str
    uu_id: str
    amount: float
    entry_type: str
    adjustment_type: str
    doc_type: str
    verified: bool = True
    status: str = ERPEntityStatus.INGESTED
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None
    invoice_number: Optional[str] = None
    invoice_amount: Optional[float] = None
    invoice_date: Optional[date] = None
    cost_center_id: Optional[str] = None

    def to_json(self):
        return self.model_dump()

    def get_unique_identifier(self):
        return self.uu_id

    def update(self, data: dict):
        self.remarks = data.get("remarks")
        self.amount = data.get("amount")
        self.hotel_code = data.get("hotel_code")
        self.invoice_number = data.get("invoice_number")
        self.invoice_amount = data.get("invoice_amount")
        self.invoice_date = data.get("invoice_date")
        self.entry_type = data.get("entry_type")
        self.adjustment_type = data.get("adjustment_type")
        self.doc_type = data.get("doc_type")
        self.posting_date = data.get("posting_date")
        self.verified = True
        self.status = ERPEntityStatus.INGESTED

    class Config:
        from_attributes = True
        validate_by_name = True
