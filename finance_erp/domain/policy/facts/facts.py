from treebo_commons.utils import dateutils


class Facts(object):
    """
    Facts used by rules defined for various action.
    If the way of deriving facts for any rule is different from the one implemented in this, create a subclass, and
    override that method. Every rule can take this class or a subclass instance as Fact.
    """

    def __init__(
        self,
        action_payload=None,
        current_time=None,
        user_type=None,
        hotel_id=None,
        **aggregates
    ):
        self.current_time = (
            current_time if current_time else dateutils.current_datetime()
        )
        self.user_type = user_type
        self.hotel_id = hotel_id
        self.action_payload = action_payload
        self.aggregates = aggregates
