# -*- coding: utf-8 -*-
from collections import namedtuple
from datetime import date, datetime
from typing import ClassVar, Optional

from pydantic import field_validator
from treebo_commons.flask_audit.plugin import change_tracker

from finance_erp.domain.base import ChangeTrackerMixin, ERPEntityMixin
from finance_erp.domain.payment.models import PGPaymentStatus
from finance_erp.domain.payment.value_objects import BookingOwner


@change_tracker
class PGPaymentEntity(ERPEntityMixin, ChangeTrackerMixin):

    PaymentGroupKey: ClassVar[namedtuple] = namedtuple(
        "PaymentGroupKey",
        ["posting_date", "paymode"],
    )

    pg_charges: Optional[float] = None
    pg_transaction_id: Optional[str] = None
    uu_id: str
    reference_number: Optional[str] = None
    hotel_code: str
    pg_tax: Optional[float] = None
    platform_fees: Optional[float] = None
    paid_by: str
    paid_to: str
    payment_type: Optional[str] = None
    payment_amount: float
    paymode: str
    paymode_type: Optional[str] = None
    payor_entity: Optional[str] = None
    booker_entity: Optional[str] = None
    athena_code: Optional[str] = None
    payor_name: str
    hotel_name: str
    invoice_id: Optional[str] = None
    check_in: Optional[date] = None
    check_out: Optional[date] = None
    channel: str
    sub_channel: str
    seller_model: str
    refund_reason: Optional[str] = None
    original_booking_amount: float
    payment_date: Optional[date] = None
    booking_owner: Optional[BookingOwner] = None
    posting_date: Optional[date] = None
    aggregation_id: Optional[str] = None
    aggregated_at: Optional[datetime] = None
    status: str = PGPaymentStatus.INGESTED
    is_advance: bool = False
    verified: bool = True
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None

    @field_validator(
        "check_in", "check_out", "payment_date", "posting_date", mode="before"
    )
    @classmethod
    def parse_empty_date(cls, v):
        if v == "":
            return None
        return v

    def to_json(self):
        return self.model_dump()

    def get_unique_identifier(self):
        return self.uu_id

    def mark_as_aggregated(self, aggregation_id):
        self.status = PGPaymentStatus.AGGREGATED
        self.aggregated_at = datetime.now()
        self.aggregation_id = aggregation_id

    def aggregation_group_key(self):
        return self.PaymentGroupKey(
            posting_date=self.posting_date,
            paymode=self.paymode,
        )

    def update(self, data: dict):
        self.pg_charges = data.get("pg_charges")
        self.hotel_code = data.get("hotel_code")
        self.pg_tax = data.get("pg_tax")
        self.platform_fees = data.get("platform_fees")
        self.reference_number = data.get("reference_number")
        self.paid_by = data.get("paid_by")
        self.paid_to = data.get("paid_to")
        self.payment_type = data.get("payment_type")
        self.payment_amount = data.get("payment_amount")
        self.paymode = data.get("paymode")
        self.paymode_type = data.get("paymode_type")
        self.payor_entity = data.get("payor_entity")
        self.booker_entity = data.get("booker_entity")
        self.booking_owner = (
            BookingOwner(
                name=data["booking_owner"].get("name"),
                email=data["booking_owner"].get("email"),
                phone=data["booking_owner"].get("phone"),
            )
            if data.get("booking_owner")
            else None
        )
        self.athena_code = data.get("athena_code")
        self.payor_name = data.get("payor_name")
        self.hotel_name = data.get("hotel_name")
        self.invoice_id = data.get("invoice_id")
        self.check_in = data.get("check_in")
        self.check_out = data.get("check_out")
        self.channel = data.get("channel")
        self.sub_channel = data.get("sub_channel")
        self.seller_model = data.get("seller_model")
        self.refund_reason = data.get("refund_reason")
        self.original_booking_amount = data.get("original_booking_amount")
        self.is_advance = data.get("is_advance")
        self.posting_date = data.get("posting_date")
        self.payment_date = data.get("payment_date")
        self.verified = True

    class Config:
        from_attributes = True
        validate_by_name = True
        arbitrary_types_allowed = True
