# -*- coding: utf-8 -*-
from datetime import date, datetime
from typing import Optional

from treebo_commons.flask_audit.plugin import change_tracker

from finance_erp.domain.base import ChangeTrackerMixin, ERPEntityMixin
from finance_erp.domain.payment.models import PGPaymentStatus
from finance_erp.domain.payment.value_objects import BookingOwner

UPDATABLE_FIELDS = ("verified",)


@change_tracker
class PGPaymentSummaryEntity(ERPEntityMixin, ChangeTrackerMixin):
    pg_charges: Optional[float] = None
    pg_transaction_id: Optional[str] = None
    uu_id: str
    reference_number: str
    hotel_code: str
    pg_tax: Optional[float] = None
    platform_fees: Optional[float] = None
    paid_by: str
    paid_to: str
    payment_type: str
    payment_amount: float
    paymode: str
    paymode_type: Optional[str] = None
    payor_entity: Optional[str] = None
    booker_entity: Optional[str] = None
    athena_code: Optional[str] = None
    payor_name: str
    hotel_name: str
    invoice_id: Optional[str] = None
    check_in: Optional[date] = None
    check_out: Optional[date] = None
    channel: str
    sub_channel: str
    seller_model: str
    refund_reason: Optional[str] = None
    original_booking_amount: float
    payment_date: Optional[date] = None
    booking_owner: Optional[BookingOwner] = None
    posting_date: Optional[date] = None
    is_advance: bool = False
    verified: bool = True
    status: str = PGPaymentStatus.INGESTED
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None
    cost_center_id: Optional[str] = None

    def to_json(self):
        return self.model_dump()

    def get_unique_identifier(self):
        return self.uu_id

    def update(self, data: dict):
        self.pg_charges = data.get("pg_charges")
        self.hotel_code = data.get("hotel_code")
        self.pg_tax = data.get("pg_tax")
        self.reference_number = data.get("reference_number")
        self.paid_by = data.get("paid_by")
        self.paid_to = data.get("paid_to")
        self.payment_type = data.get("payment_type")
        self.payment_amount = data.get("payment_amount")
        self.paymode = data.get("paymode")
        self.paymode_type = data.get("paymode_type")
        self.payor_entity = data.get("payor_entity")
        self.booker_entity = data.get("booker_entity")
        self.booking_owner = data.get("booking_owner")
        self.athena_code = data.get("athena_code")
        self.payor_name = data.get("payor_name")
        self.hotel_name = data.get("hotel_name")
        self.invoice_id = data.get("invoice_id")
        self.check_in = data.get("check_in")
        self.check_out = data.get("check_out")
        self.channel = data.get("channel")
        self.sub_channel = data.get("sub_channel")
        self.seller_model = data.get("seller_model")
        self.refund_reason = data.get("refund_reason")
        self.original_booking_amount = data.get("original_booking_amount")
        self.is_advance = data.get("is_advance")
        self.posting_date = data.get("posting_date")
        self.payment_date = data.get("payment_date")
        self.verified = True
        self.status = PGPaymentStatus.AGGREGATED

    def aggregate(self, payment_entity):
        self.payment_amount = round(
            float(self.payment_amount)
            + (
                float(payment_entity.payment_amount)
                if payment_entity.payment_amount
                else 0.0
            ),
            2,
        )
