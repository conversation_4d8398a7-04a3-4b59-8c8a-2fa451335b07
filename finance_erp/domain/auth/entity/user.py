from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class User(BaseModel):
    email: str
    authenticated: bool = True
    tokens: Optional[dict] = None
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    def is_active(self) -> bool:
        return True

    def get_id(self) -> str:
        return self.email

    def is_authenticated(self) -> bool:
        return self.authenticated

    def is_anonymous(self) -> bool:
        return False

    class Config:
        frozen = False
