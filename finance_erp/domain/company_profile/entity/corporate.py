from collections import namedtuple
from datetime import date, datetime, timedelta
from typing import Dict, List, Optional

from treebo_commons.flask_audit.plugin import change_tracker
from treebo_commons.utils.dateutils import date_to_ymd_str

from finance_erp.common.constants import CORPORATE_BILLING_OFFSET, NavisionReports
from finance_erp.common.utils.checksum_creator import generate_check_sum
from finance_erp.domain.base import ChangeTrackerMixin, ERPEntityMixin, ERPEntityStatus
from finance_erp.domain.company_profile.value_object import (
    CommunicationSettings,
    CorporatePOC,
)

UPDATABLE_FIELDS = ("verified",)

BillingCycle = namedtuple(
    "BillingCycle",
    ["from_date", "to_date"],
)


@change_tracker
class CorporateEntity(ERPEntityMixin, ChangeTrackerMixin):
    customer_legal_name: str
    corporate_code: str
    customer_trading_name: str
    address: str
    city: Optional[str] = None
    phone_number: Optional[str] = None
    post_code: Optional[str] = None
    email: Optional[str] = None
    credit_limit: Optional[str] = None
    credit_period: Optional[int]
    country_code: Optional[str] = None
    pan: Optional[str] = None
    state_code: Optional[str] = None
    gstin: Optional[str] = None
    gst_customer_type: str
    tan_number: Optional[str] = None
    billing_period: Optional[int] = 0
    external_account_number: Optional[str] = None
    communication_settings: Optional[CommunicationSettings] = None
    corporate_pocs: Optional[List[CorporatePOC]] = None
    _next_billing_date: Optional[date] = None
    _is_billing_enabled: bool = False
    _poc_mappings: dict = {}
    verified: bool = True
    status: str = ERPEntityStatus.INGESTED
    deleted: bool = False
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None
    last_push_attempt_at: Optional[datetime] = None
    erp_remarks: Optional[str] = None
    is_new: bool = True

    def __post_init__(self):
        self.set_billing_eligibility()
        if self.is_new:
            self.set_next_billing_date()
        self._poc_mappings = {poc.designation: poc for poc in self.corporate_pocs or []}

    def to_json(self):
        """Convert the object to a JSON"""
        return {
            "customer_legal_name": self.customer_legal_name,
            "corporate_code": self.corporate_code,
            "customer_trading_name": self.customer_trading_name,
            "address": self.address,
            "city": self.city,
            "phone_number": self.phone_number,
            "post_code": self.post_code,
            "email": self.email,
            "credit_limit": self.credit_limit,
            "credit_period": self.credit_period,
            "country_code": self.country_code,
            "pan": self.pan,
            "state_code": self.state_code,
            "gstin": self.gstin,
            "gst_customer_type": self.gst_customer_type,
            "tan_number": self.tan_number,
            "external_account_number": self.external_account_number,
            "verified": self.verified,
            "status": self.status,
            "deleted": self.deleted,
            "last_push_attempt_at": str(self.last_push_attempt_at)
            if self.last_push_attempt_at
            else None,
            "erp_remarks": self.erp_remarks,
        }

    def get_unique_identifier(self) -> str:
        return self.corporate_code

    @property
    def next_billing_date(self):
        return self._next_billing_date

    @property
    def data_checksum(self):
        return generate_check_sum(NavisionReports.CORPORATE_REPORT, data_class=self)

    def update(self, other):
        assert (
            isinstance(other, CorporateEntity)
            and other.corporate_code == self.corporate_code
        ), "Invalid object for update"
        is_billing_period_changed = self.billing_period != other.billing_period
        self.customer_legal_name = other.customer_legal_name
        self.customer_trading_name = other.customer_trading_name
        self.address = other.address
        self.city = other.city
        self.phone_number = other.phone_number
        self.post_code = other.post_code
        self.email = other.email
        self.credit_limit = other.credit_limit
        self.credit_period = other.credit_period
        self.country_code = other.country_code
        self.pan = other.pan
        self.state_code = other.state_code
        self.gstin = other.gstin
        self.gst_customer_type = other.gst_customer_type
        self.tan_number = other.tan_number
        self.communication_settings = other.communication_settings
        self.corporate_pocs = other.corporate_pocs
        self.billing_period = other.billing_period
        self.external_account_number = other.external_account_number
        self.verified = True
        if self.pushed:
            self.erp_remarks = f"{self.erp_remarks} on {self.last_push_attempt_at}, data change after that"
            self.last_push_attempt_at = None
        self.status = ERPEntityStatus.PUSHED
        billing_eligibility_changed = self.set_billing_eligibility()
        if billing_eligibility_changed or is_billing_period_changed:
            self.set_next_billing_date()

    def set_next_billing_date(self):
        if not self._is_billing_enabled:
            self._next_billing_date = None
            return
        current_date = datetime.now()
        first_billing_date = current_date.replace(day=1) + timedelta(
            days=(CORPORATE_BILLING_OFFSET - 1)
        )
        while first_billing_date.month == current_date.month:
            if first_billing_date > current_date:
                self._next_billing_date = date_to_ymd_str(first_billing_date.date())
                return
            first_billing_date += timedelta(days=self.billing_period)

        first_billing_date = first_billing_date.replace(day=1) + timedelta(
            days=(CORPORATE_BILLING_OFFSET - 1)
        )
        self._next_billing_date = date_to_ymd_str(first_billing_date.date())

    def wrap_up_current_billing_cycle(self) -> None:
        self.set_next_billing_date()

    def set_billing_eligibility(self):
        is_billing_enabled = (
            self.billing_period and self.is_stay_summary_dispatch_enabled
        )
        print("self.billing_period", self.billing_period)
        print("self.is_stay_summary_dispatch_enabled", self.is_stay_summary_dispatch_enabled)
        print("is_billing_enabled", is_billing_enabled)
        if self.is_billing_enabled != is_billing_enabled:
            self._is_billing_enabled = is_billing_enabled
            return True
        return False

    @property
    def is_stay_summary_dispatch_enabled(self):
        return (
            self.communication_settings
            and self.communication_settings.should_dispatch_stay_summary()
        )

    @property
    def is_invoice_dispatch_enabled(self):
        return (
            self.communication_settings
            and self.communication_settings.should_dispatch_invoice_on_checkout()
        )

    @property
    def invoice_types_for_stay_summary(self):
        if not self.communication_settings:
            return None
        return self.communication_settings.invoice_types_to_dispatch_with_stay_summary()

    @property
    def invoice_types_for_checkout_dispatch(self):
        if not self.communication_settings:
            return None
        return self.communication_settings.invoice_types_to_dispatch_on_checkout()

    @property
    def should_dispatch_booking_request_with_stay_summary(self):
        return (
            self.communication_settings
            and self.communication_settings.should_dispatch_booking_request_with_stay_summary
        )

    @property
    def should_dispatch_booking_request_invoice_or_cn(self):
        return self.should_dispatch_booking_request_with_stay_summary

    @property
    def billing_cycle(self):
        if not self._next_billing_date:
            return None
        to_date = self._next_billing_date - timedelta(days=CORPORATE_BILLING_OFFSET)
        from_date = to_date.replace(day=1)
        return BillingCycle(from_date, to_date)

    @property
    def is_billing_enabled(self):
        return self._is_billing_enabled

    def get_poc_by_designation(self, designation) -> Optional[CorporatePOC]:
        return self._poc_mappings.get(designation)