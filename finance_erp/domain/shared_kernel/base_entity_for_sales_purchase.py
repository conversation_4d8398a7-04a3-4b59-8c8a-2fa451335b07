# -*- coding: utf-8 -*-
from datetime import date
from typing import Optional

from pydantic import BaseModel


class BaseEntity(BaseModel):
    entry_type: str
    order_date: Optional[date] = None
    posting_date: Optional[date] = None
    reference_number: str
    state_code: str
    unit_price: float
    tax_percentage: float
    hotel_name: str
    check_in: Optional[date] = None
    check_out: Optional[date] = None
    stay_days: Optional[str] = None
    room_type: Optional[str] = None
    occupancy: str
    guest_name: str
    uvid_date: Optional[date] = None
    invoice_number: str
    total_invoice_amount: float
    hotel_code: str
    unique_ref_id: str
    source: str
    sub_source: str
