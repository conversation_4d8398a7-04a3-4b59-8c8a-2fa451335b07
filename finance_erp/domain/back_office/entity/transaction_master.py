# -*- coding: utf-8 -*-
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, field_validator
from treebo_commons.flask_audit.plugin import change_tracker

from finance_erp.domain.back_office.value_objects import TransactionMetaData
from finance_erp.domain.base import ChangeTrackerMixin


@change_tracker
class TransactionMasterEntity(ChangeTrackerMixin, BaseModel):
    hotel_id: str
    erp_name: Optional[str] = None
    transaction_id: Optional[int] = None
    gl_code: Optional[str] = None
    is_active: bool = True
    merge_gl_entries: bool = True
    particulars: Optional[str] = None
    identifier_name: Optional[str] = None
    display_name: Optional[str] = None
    revenue_center: Optional[str] = None
    identifier: Optional[str] = None
    transaction_type: Optional[str] = None
    transaction_metadata: Optional[TransactionMetaData] = None
    deleted: bool = False
    is_dirty: bool = False
    is_new: bool = True
    created_at: Optional[datetime] = None
    modified_at: Optional[datetime] = None

    @field_validator("identifier", mode="before")
    def parse_identifier(cls, value):
        if isinstance(value, int):
            return str(value)
        return value

    def to_json(self) -> dict:
        return self.model_dump()

    def get_unique_identifier(self) -> Optional[int]:
        return self.transaction_id

    def mark_dirty(self) -> None:
        self.is_dirty = True

    def update(self, other: "TransactionMasterEntity") -> None:
        self.update_display_name(other.display_name)

    def update_display_name(self, display_name: Optional[str]) -> None:
        if self.display_name != display_name:
            self.display_name = display_name
            self.mark_dirty()

    class Config:
        from_attributes = True
        validate_by_name = True
