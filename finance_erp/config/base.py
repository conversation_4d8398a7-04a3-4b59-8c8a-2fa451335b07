# pylint: disable=invalid-name
import os


class Config:
    ENV = "local"
    DEBUG = True
    LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
    MAX_THREAD_FOR_PDF_GENERATION = os.environ.get("MAX_THREAD_FOR_PDF_GENERATION", 4)

    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    LOG_ROOT = f"{root_dir}/logs"
    LOG_REQUEST_ID_GENERATE_IF_NOT_FOUND = True

    FLASK_ADMIN_SWATCH = "flatly"
    SECRET_KEY = "_bXQcNMv<2J[omci$-J}ptSw>)z+Vw"

    NAVISION_ERROR_REPORT_RECEIVER_LIST = ["<EMAIL>"]
    FINANCE_ERP_ALERTS = "*******************************************************************************"
    AUTH = {
        "CLIENT_ID": os.environ.get("CLIENT_ID"),
        "CLIENT_SECRET": os.environ.get("CLIENT_SECRET"),
        "AUTH_URI": "https://accounts.google.com/o/oauth2/auth",
        "TOKEN_URI": "https://accounts.google.com/o/oauth2/token",
        "USER_INFO": "https://www.googleapis.com/userinfo/v2/me",
        "SCOPE": ["email", "profile"],
    }
