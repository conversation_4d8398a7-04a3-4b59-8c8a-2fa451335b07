from datetime import date
from typing import Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import date_to_ymd_str, ymd_str_to_date

from finance_erp.common.schema.common import BusinessCentralDataPushSchema


class HotelAdjustmentPushSchema(BaseModel):
    hotel_code: str = Field(..., alias="HotelCode")
    posting_date: date = Field(..., alias="PostingDate")
    amount: float = Field(..., alias="JVAmount")
    invoice_number: Optional[str] = Field(None, alias="InvoiceNo")
    invoice_date: Optional[date] = Field(None, alias="InvoiceDate")
    invoice_amount: Optional[float] = Field(None, alias="InvoiceAmount")
    remarks: Optional[str] = Field(None, alias="Remarks")
    adjustment_type: Optional[str] = Field(..., alias="AdjustmentType")
    entry_type: Optional[str] = Field(..., alias="EntryType")
    doc_type: Optional[str] = Field(..., alias="DocType")
    cost_center_id: Optional[str] = Field(None, alias="CostCenterID")

    @field_validator("posting_date", mode="before")
    def parse_posting_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("invoice_date", mode="before")
    def parse_invoice_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("invoice_amount", mode="before")
    def parse_invoice_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class HotelAdjustmentPushRequestSchema(
    HotelAdjustmentPushSchema, BusinessCentralDataPushSchema
):
    ...


class HotelAdjustmentResponseSchema(HotelAdjustmentPushSchema):
    posted_on: Optional[date] = Field(default=None, alias="PostedOn")
    status: Optional[str] = None
    verified: Optional[bool] = None
    uu_id: Optional[str] = None

    @field_validator("posted_on", mode="before")
    def parse_posted_on(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for field, value in data.items():
            if isinstance(value, date):
                data[field] = date_to_ymd_str(value)
            if value is None:
                data[field] = ""
        return data

    class Config:
        validate_by_name = True
        from_attributes = True


class HotelAdjustmentUpdateSchema(BaseModel):
    uu_id: str
    verified: Optional[bool] = None
