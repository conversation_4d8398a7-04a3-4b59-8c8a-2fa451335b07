from datetime import date, time
from typing import List, Optional

from pydantic import BaseModel, Field, field_validator
from treebo_commons.utils.dateutils import date_to_ymd_str, ymd_str_to_date


class TaxDetailSchema(BaseModel):
    tax_type: Optional[str] = None
    tax_value: Optional[float] = None
    tax_amount: Optional[float] = None
    tax_code: Optional[str] = None

    @field_validator("tax_amount", mode="before")
    def parse_tax_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("tax_value", mode="before")
    def parse_tax_value(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class POSRevenueItemRequestSchema(BaseModel):
    interface_id: str
    interface_name: str
    hotel_id: str
    bill_id: str
    reservation_id: Optional[str] = None
    guest_name: Optional[str] = None
    amount: float
    tax: Optional[float] = None
    sku_category: str
    hsn_code: str
    payment_method: str
    pos_bill_date: date
    tax_details: Optional[List[TaxDetailSchema]] = None
    pos_bill_time: str
    workstation_id: Optional[str] = None
    waiter_id: Optional[str] = None
    revenue_center: Optional[str] = None
    serving_time: Optional[str] = None
    sku_id: Optional[str] = None
    payment_mode_sub_type: Optional[str] = None

    @field_validator("amount", mode="before")
    def parse_amount(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("tax", mode="before")
    def parse_tax(cls, v):
        if isinstance(v, str):
            return float(v)
        return v

    @field_validator("pos_bill_date", mode="before")
    def parse_pos_bill_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    @field_validator("pos_bill_time", mode="before")
    def parse_pos_bill_time(cls, v):
        if isinstance(v, time):
            return str(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True


class POSRevenueItemResponseSchema(POSRevenueItemRequestSchema):
    uu_id: str
    verified: bool = Field(default=True)
    deleted: bool = Field(default=False)

    def model_dump(self, *args, **kwargs):
        data = super().model_dump(*args, **kwargs)
        for field, value in data.items():
            value = data.get(field)
            if isinstance(value, date):
                data[field] = date_to_ymd_str(value)
            if value is None:
                data[field] = ""
        return data

    class Config:
        validate_by_name = True
        from_attributes = True


class POSRevenueItemSearchSchema(BaseModel):
    hotel_id: Optional[str] = None
    bill_id: Optional[str] = None
    reservation_id: Optional[str] = None
    pos_bill_date: Optional[date] = None
    limit: int = Field(default=20)
    offset: int = Field(default=0)

    @field_validator("pos_bill_date", mode="before")
    def parse_pos_bill_date(cls, v):
        if isinstance(v, str):
            return ymd_str_to_date(v)
        return v

    class Config:
        validate_by_name = True
        from_attributes = True
