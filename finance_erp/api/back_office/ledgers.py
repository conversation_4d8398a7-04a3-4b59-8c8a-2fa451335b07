import logging

from flask import request
from ths_common.exceptions import ValidationException
from treebo_commons.request_tracing.context import get_current_hotel_id
from treebo_commons.utils.dateutils import date_to_ymd_str

from core.common.api.treebo_api import TreeboBaseAPI
from core.common.utils.request_parser import RequestTypes, schema_wrapper_parser
from finance_erp.api.schema.request.back_office.ledgers import (
    GetLedgersFileRequestSchema,
    LedgersFileGenerationRequestSchema,
)
from finance_erp.application.back_office.comand_handlers.create_daily_ledgers import (
    DailyLedgerCreationCommandHandler,
)
from finance_erp.application.back_office.comand_handlers.get_ledgers_file import (
    FetchLedgersFileCommandHandler,
)
from finance_erp.application.back_office.dtos.ledgers_dto import (
    GenerateLedgersFileDto,
    GetLedgersFileDto,
)
from finance_erp.application.hotel_settings.tenant_settings import TenantSettings
from finance_erp.async_job.job_scheduler_service import JobSchedulerService
from finance_erp.common.decorators import session_manager
from finance_erp.common.error_codes import ApplicationErrors
from finance_erp.common.schema.back_office.ledgers_file import LedgersFileResponseSchema
from finance_erp.common.schema.common import serialize_with_schema
from finance_erp.domain.policy.engine import RuleEngine
from finance_erp.domain.policy.facts.facts import Facts
from object_registry import inject, locate_instance

logger = logging.getLogger(__name__)


class LedgersFileView(TreeboBaseAPI):
    @schema_wrapper_parser(GetLedgersFileRequestSchema, param_type=RequestTypes.ARGS)
    @inject(command_handler=FetchLedgersFileCommandHandler)
    def get(
        self, command_handler: FetchLedgersFileCommandHandler, parsed_request, **kwargs
    ):
        hotel_id = get_current_hotel_id()
        self._do_privilege_check(hotel_id)

        request_dto = GetLedgersFileDto(**parsed_request, hotel_id=hotel_id)
        self._validate_erp_name(request_dto.erp_name, hotel_id)

        data = command_handler.handle(request_dto)
        response = serialize_with_schema(LedgersFileResponseSchema, data, many=True)
        return {"data": response}

    @session_manager(commit=True)
    @schema_wrapper_parser(LedgersFileGenerationRequestSchema)
    @inject(
        job_scheduler=JobSchedulerService,
        ledger_generator=DailyLedgerCreationCommandHandler,
    )
    def post(
        self,
        job_scheduler: JobSchedulerService,
        ledger_generator: DailyLedgerCreationCommandHandler,
        parsed_request,
        **kwargs,
    ):
        hotel_id = get_current_hotel_id()
        self._do_privilege_check(hotel_id)

        request_dto = GenerateLedgersFileDto(**parsed_request, hotel_id=hotel_id)
        self._validate_erp_name(request_dto.erp_name, hotel_id)

        if self._async:
            job = job_scheduler.schedule_backoffice_ledger_generation_job(
                date=date_to_ymd_str(date_time=request_dto.date),
                hotel_id=request_dto.hotel_id,
                erp_name=request_dto.erp_name,
                refresh_transaction_master=request_dto.refresh_transaction_master,
            )
            return {"data": job.job_id}
        files = ledger_generator.handle(
            date=request_dto.date,
            hotel_id=request_dto.hotel_id,
            refresh_transaction_master=request_dto.refresh_transaction_master,
            erp_name=request_dto.erp_name,
        )
        response = serialize_with_schema(
            LedgersFileResponseSchema, data=files, many=True
        )
        return {"data": response}

    @staticmethod
    def _do_privilege_check(hotel_id):
        if not hotel_id:
            raise ValidationException(description="X-Hotel-Id is missing")
        user = request.headers.get("X-USER-TYPE", None)
        if not user:
            raise ValidationException(description="X-User-Type is missing")
        RuleEngine.action_allowed(
            action="ledgers_data_rule",
            facts=Facts(user_type=user, hotel_id=hotel_id),
            fail_on_error=True,
        )

    @staticmethod
    def _validate_erp_name(erp_name, hotel_id):
        tenant_settings = locate_instance(TenantSettings)
        enabled_erps = tenant_settings.get_enabled_backoffice_erps(hotel_id)
        if erp_name not in enabled_erps:
            raise ValidationException(
                ApplicationErrors.INVALID_BACKOFFICE_ERP,
                extra_payload=dict(erp_name=erp_name),
            )
