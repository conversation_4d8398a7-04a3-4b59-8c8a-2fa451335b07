#!/bin/bash

TENANT_ID="$1"
CAN_CONSUME_SERIAL_JOB="$2"

pwd
echo "Starting async job executor ${FLASK_APP} ${PYTHONPATH}"
echo "Tenant Id: $TENANT_ID"
echo "Job Consumer Type: $CAN_CONSUME_SERIAL_JOB"

if [ "$CAN_CONSUME_SERIAL_JOB" = "can_consume_serial_job" ]; then
    newrelic-admin run-program flask async_job_executor --tenant_id="$TENANT_ID" --consume-serial-job
else
    newrelic-admin run-program flask async_job_executor --tenant_id="$TENANT_ID"
fi
